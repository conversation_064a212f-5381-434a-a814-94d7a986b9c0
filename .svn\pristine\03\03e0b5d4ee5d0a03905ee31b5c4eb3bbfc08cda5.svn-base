{"name": "optional", "version": "0.1.4", "homepage": "http://segomos.com/", "main": "optional", "repository": {"type": "git", "url": "git+ssh://**************/tony-o/node-optional.git"}, "description": "Allows you to optionally include modules without surrounding everything with 'try/catch'", "keywords": ["include", "optional include", "optional", "require", "exists"], "license": "MIT", "gitHead": "584a7745bf0b3f5a59e3432ea39d543581478ae5", "bugs": {"url": "https://github.com/tony-o/node-optional/issues"}, "_id": "optional@0.1.4", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.4", "_npmUser": {"name": "segomos", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-gtvrrCfkE08wKcgXaVwQVgwEQ8vel2dc5DDBn9RLQZ3YtmtkBss6A2HY6BnJH4N/4Ku97Ri/SF8sNWE2225WJw==", "shasum": "cdb1a9bedc737d2025f690ceeb50e049444fd5b3", "tarball": "https://registry.npmjs.org/optional/-/optional-0.1.4.tgz"}, "maintainers": [{"name": "segomos", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/optional-0.1.4.tgz_1499963470149_0.9972208293620497"}, "directories": {}, "_shasum": "cdb1a9bedc737d2025f690ceeb50e049444fd5b3", "_resolved": "https://registry.npmjs.org/optional/-/optional-0.1.4.tgz", "_from": "optional@>=0.1.3 <0.2.0", "readme": "ERROR: No README data found!"}