{"_args": [["mkdirp@0.5.5", "/Users/<USER>/07game/qixinggame/backstate/game-server"]], "_from": "mkdirp@0.5.5", "_id": "mkdirp@0.5.5", "_inBundle": false, "_integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "_location": "/utility/mkdirp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "mkdirp@0.5.5", "name": "mkdirp", "escapedName": "mkdirp", "rawSpec": "0.5.5", "saveSpec": null, "fetchSpec": "0.5.5"}, "_requiredBy": ["/utility"], "_resolved": "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1587535418745&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz", "_spec": "0.5.5", "_where": "/Users/<USER>/07game/qixinggame/backstate/game-server", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": {"mkdirp": "bin/cmd.js"}, "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "dependencies": {"minimist": "^1.2.5"}, "description": "Recursively mkdir, like `mkdir -p`", "devDependencies": {"mock-fs": "^3.7.0", "tap": "^5.4.2"}, "files": ["bin", "index.js"], "homepage": "https://github.com/substack/node-mkdirp#readme", "keywords": ["mkdir", "directory"], "license": "MIT", "main": "index.js", "name": "mkdirp", "publishConfig": {"tag": "legacy"}, "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "version": "0.5.5"}